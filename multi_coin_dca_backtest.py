# multi_coin_dca_backtest.py
# 多币种定投回测脚本
import math, datetime as dt, statistics as st
import requests
import pandas as pd
import numpy as np
from binance.spot import Spot
import os
import time
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import warnings
warnings.filterwarnings('ignore')

# ---------------- 参数配置 ----------------
START = "2020-01-01"
END   = "2025-08-13"
FREQ  = "W-MON"                           # 每周一定投：可改 "D" / "SM" / "M"
BASE_CONTRIB = 100.0                      # 基准每期投入（USD）
FEE_BPS = 0.1                             # 单边费率，例：0.1% => 10 bps
RFR = 0.0                                 # 无风险利率（年化）

# 支持的币种列表
COINS = ['ETH', 'TRX', 'BNB', 'ADA', 'SOL', 'DOGE', 'XRP']
# ----------------------------------------

client = Spot()

def get_binance_spot_klines(symbol, direction='forward'):
    """获取币安现货K线数据"""
    startTime = 1577808000000  # 2020-01-01
    total = 0
    file_name = f"{symbol}_1d_spot.csv"
    
    if not os.path.exists(file_name):
        with open(file_name, "w") as fp:
            print(
                "open_time,open,high,low,close,volume,close_time,quote_volume,count,taker_buy_volume,taker_buy_quote_volume,ignore",
                file=fp)
    else:
        try:
            with open(file_name, "r") as fp:
                all_lines = fp.readlines()
                if len(all_lines) > 1:
                    last_line = all_lines[-1]
                    all_items = last_line.split(",")
                    startTime = int(all_items[0]) + 1
        except Exception as e:
            print(f"读取文件错误: {e}")
    
    while True:
        try:
            result = client.klines(symbol, interval="1d", startTime=startTime, limit=1000)
        except Exception as e:
            print(f"API请求错误: {e}")
            if hasattr(e, 'error_code'):
                if e.error_code == -1121:
                    break
                if e.error_code == -1003:
                    time.sleep(300)
            time.sleep(10)
            continue
        
        if not result:
            break
            
        with open(file_name, "a") as fp:
            for item in result:
                print(",".join(str(x) for x in item), file=fp)
        
        total += len(result)
        print(f"{symbol}: {total} 条数据")
        
        if len(result) < 1000:
            break
            
        startTime = result[-1][0] + 1

def get_price_daily(symbol: str):
    """获取日价格数据"""
    symbol_usdt = f"{symbol}USDT"
    file_path = f'{symbol_usdt}_1d_spot.csv'
    
    # 如果文件不存在，先下载数据
    if not os.path.exists(file_path):
        print(f"正在下载 {symbol_usdt} 数据...")
        get_binance_spot_klines(symbol_usdt)
    
    try:
        data = pd.read_csv(file_path)
        p = data[['open_time', 'open']]
        p["date"] = pd.to_datetime(p["open_time"], unit="ms").dt.date
        p = p.rename(columns={"open": "price"})
        p = p[["date","price"]].drop_duplicates("date").set_index("date").sort_index()
        
        # 限制时间范围
        p = p.loc[(p.index>=pd.to_datetime(START).date()) & (p.index<=pd.to_datetime(END).date())]
        return p
    except Exception as e:
        print(f"读取 {symbol} 价格数据失败: {e}")
        return pd.DataFrame()

def make_daily(p):
    """处理日价格数据，填充缺失日期"""
    if p.empty:
        return pd.DataFrame()
    
    # 确保每日数据（向前填充价格）
    idx = pd.date_range(p.index.min(), p.index.max(), freq="D").date
    d = p.reindex(idx).ffill()
    d.index.name = "date"
    d = d.rename(columns={"price": "close"})
    return d

def schedule_invest_dates(d):
    """生成定投日期"""
    if d.empty:
        return []
    return pd.date_range(d.index.min(), d.index.max(), freq=FREQ).date

def backtest_coin(symbol):
    """单币种定投回测"""
    print(f"\n正在回测 {symbol}...")
    
    # 获取价格数据
    p = get_price_daily(symbol)
    if p.empty:
        print(f"{symbol} 数据获取失败")
        return None
    
    d = make_daily(p)
    if d.empty:
        print(f"{symbol} 数据处理失败")
        return None
    
    dates = schedule_invest_dates(d)
    
    # 初始化
    qty, cost = 0.0, 0.0
    invest_count = 0
    invest_days = []
    
    # 定投循环
    for day in dates:
        if day not in d.index:
            continue
        
        px = d.loc[day, "close"]
        fee_factor = 1 - FEE_BPS / 100.0  # 修正费率计算
        
        usd = BASE_CONTRIB
        q = usd / px * fee_factor
        qty += q
        cost += usd
        invest_count += 1
        invest_days.append(day)
    
    if invest_count == 0:
        print(f"{symbol} 没有有效的投资日期")
        return None
    
    # 计算最终价值
    last_day = d.index[-1]
    last_px = d.loc[last_day, "close"]
    final_value = qty * last_px
    
    # 构建权益曲线
    start_date = min(invest_days)
    end_date = max(last_day, max(invest_days))
    full_idx = pd.date_range(start_date, end_date, freq="D")
    equity = pd.Series(index=full_idx, dtype=float)
    
    current_qty = 0.0
    for date in full_idx:
        date = date.date()
        
        # 获取当日价格
        if date in d.index:
            px = d.loc[date, "close"]
        else:
            px = d.loc[d.index[d.index <= date][-1], "close"] if any(d.index <= date) else np.nan
        
        # 如果是投资日，增加持仓
        if date in invest_days:
            fee_factor = 1 - FEE_BPS / 100.0
            q = BASE_CONTRIB / px * fee_factor
            current_qty += q
        
        # 计算当日权益
        if not np.isnan(px):
            equity[date] = current_qty * px
        else:
            equity[date] = equity.ffill().iloc[-1] if not equity.empty else 0
    
    # 计算统计指标
    total_days = (equity.index[-1] - equity.index[0]).days
    years = total_days / 365.25 if total_days > 0 else 1
    
    cagr = (final_value / cost) ** (1 / years) - 1 if cost > 0 and years > 0 else 0
    
    # 计算波动率和夏普比率
    returns = equity.pct_change().dropna()
    returns = returns.replace([np.inf, -np.inf], np.nan).dropna()
    
    if len(returns) > 1:
        ann_vol = returns.std() * np.sqrt(365)
        sharpe = (cagr - RFR) / (ann_vol + 1e-7)
    else:
        ann_vol = 0
        sharpe = 0
    
    # 计算最大回撤
    peak = equity.cummax()
    drawdown = (equity - peak) / peak
    max_dd = drawdown.min()
    
    result = {
        "symbol": symbol,
        "final_value": final_value,
        "total_cost": cost,
        "total_return": (final_value / cost - 1) * 100 if cost > 0 else 0,
        "cagr": cagr * 100,
        "ann_vol": ann_vol * 100,
        "sharpe": sharpe,
        "max_dd": max_dd * 100,
        "invest_count": invest_count,
        "equity_curve": equity
    }
    
    return result

def run_multi_coin_backtest():
    """运行多币种回测"""
    print("=== 多币种定投回测 ===")
    print(f"回测期间: {START} 至 {END}")
    print(f"定投频率: {FREQ}")
    print(f"每期投入: ${BASE_CONTRIB}")
    print(f"手续费: {FEE_BPS}%")
    
    results = []
    
    for coin in COINS:
        result = backtest_coin(coin)
        if result:
            results.append(result)
    
    if not results:
        print("没有成功回测的币种")
        return
    
    # 创建结果汇总表
    summary_df = pd.DataFrame([
        {
            "币种": r["symbol"],
            "最终资产(USD)": f"${r['final_value']:,.2f}",
            "累计投入(USD)": f"${r['total_cost']:,.2f}",
            "总收益(%)": f"{r['total_return']:.2f}%",
            "年化收益(%)": f"{r['cagr']:.2f}%",
            "年化波动(%)": f"{r['ann_vol']:.2f}%",
            "夏普比率": f"{r['sharpe']:.2f}",
            "最大回撤(%)": f"{r['max_dd']:.2f}%",
            "投资次数": r["invest_count"]
        }
        for r in results
    ])
    
    print("\n=== 回测结果汇总 ===")
    print(summary_df.to_string(index=False))
    
    # 保存结果到CSV
    summary_df.to_csv(f"多币种定投回测结果_{START}_至_{END}.csv", index=False, encoding='utf-8-sig')
    print(f"\n结果已保存至: 多币种定投回测结果_{START}_至_{END}.csv")
    
    return results

def plot_multi_coin_results(results):
    """绘制多币种定投结果对比图"""
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Heiti SC', 'Songti SC']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 1. 权益曲线对比
    for result in results:
        equity = result["equity_curve"]
        ax1.plot(equity.index, equity.values, label=f'{result["symbol"]}', linewidth=2)

    ax1.set_title('多币种定投权益曲线对比', fontsize=14)
    ax1.set_xlabel('日期')
    ax1.set_ylabel('资产价值 (USD)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')

    # 2. 总收益率对比
    symbols = [r["symbol"] for r in results]
    total_returns = [r["total_return"] for r in results]
    colors = plt.cm.Set3(np.linspace(0, 1, len(symbols)))

    bars = ax2.bar(symbols, total_returns, color=colors)
    ax2.set_title('总收益率对比', fontsize=14)
    ax2.set_ylabel('总收益率 (%)')
    ax2.grid(True, alpha=0.3)

    # 在柱状图上添加数值标签
    for bar, value in zip(bars, total_returns):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + max(total_returns)*0.01,
                f'{value:.1f}%', ha='center', va='bottom')

    # 3. 年化收益率 vs 波动率散点图
    cagrs = [r["cagr"] for r in results]
    vols = [r["ann_vol"] for r in results]

    scatter = ax3.scatter(vols, cagrs, c=colors, s=100, alpha=0.7)
    for i, symbol in enumerate(symbols):
        ax3.annotate(symbol, (vols[i], cagrs[i]), xytext=(5, 5),
                    textcoords='offset points', fontsize=10)

    ax3.set_title('风险收益散点图', fontsize=14)
    ax3.set_xlabel('年化波动率 (%)')
    ax3.set_ylabel('年化收益率 (%)')
    ax3.grid(True, alpha=0.3)

    # 4. 夏普比率对比
    sharpes = [r["sharpe"] for r in results]
    bars = ax4.bar(symbols, sharpes, color=colors)
    ax4.set_title('夏普比率对比', fontsize=14)
    ax4.set_ylabel('夏普比率')
    ax4.grid(True, alpha=0.3)

    # 在柱状图上添加数值标签
    for bar, value in zip(bars, sharpes):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + max(sharpes)*0.01,
                f'{value:.2f}', ha='center', va='bottom')

    plt.tight_layout()

    # 保存图片
    filename = f"多币种定投对比_{START}_至_{END}.png"
    plt.savefig(filename, dpi=200, bbox_inches='tight')
    print(f"对比图表已保存至: {filename}")

    plt.show()

def analyze_portfolio_performance(results):
    """分析投资组合表现"""
    if not results:
        return

    print("\n=== 投资组合分析 ===")

    # 计算等权重投资组合表现
    total_investment = sum(r["total_cost"] for r in results)
    total_value = sum(r["final_value"] for r in results)
    portfolio_return = (total_value / total_investment - 1) * 100

    print(f"等权重投资组合:")
    print(f"  总投入: ${total_investment:,.2f}")
    print(f"  总价值: ${total_value:,.2f}")
    print(f"  总收益率: {portfolio_return:.2f}%")

    # 找出最佳和最差表现
    best_performer = max(results, key=lambda x: x["total_return"])
    worst_performer = min(results, key=lambda x: x["total_return"])

    print(f"\n最佳表现: {best_performer['symbol']} ({best_performer['total_return']:.2f}%)")
    print(f"最差表现: {worst_performer['symbol']} ({worst_performer['total_return']:.2f}%)")

    # 风险调整后收益最佳
    best_sharpe = max(results, key=lambda x: x["sharpe"])
    print(f"最佳夏普比率: {best_sharpe['symbol']} ({best_sharpe['sharpe']:.2f})")

if __name__ == "__main__":
    results = run_multi_coin_backtest()

    if results:
        # 绘制对比图表
        try:
            plot_multi_coin_results(results)
        except Exception as e:
            print(f"绘图失败: {e}")

        # 分析投资组合表现
        analyze_portfolio_performance(results)
