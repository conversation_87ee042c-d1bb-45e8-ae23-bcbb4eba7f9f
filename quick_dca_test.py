# quick_dca_test.py
# 快速多币种定投回测脚本（使用现有数据）
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

# ---------------- 参数配置 ----------------
START = "2020-01-01"
END   = "2025-08-13"
FREQ  = "W-MON"                           # 每周一定投
BASE_CONTRIB = 100.0                      # 每期投入100美元
FEE_BPS = 0.1                             # 手续费0.1%

# 检查现有数据文件的币种
def check_available_coins():
    """检查当前目录下可用的币种数据"""
    available_coins = []
    
    # 检查BTC定投目录
    btc_dir = "BTC定投"
    if os.path.exists(btc_dir):
        for file in os.listdir(btc_dir):
            if file.endswith("_1d_spot.csv"):
                symbol = file.replace("_1d_spot.csv", "").replace("USDT", "")
                available_coins.append(symbol)
    
    # 检查当前目录
    for file in os.listdir("."):
        if file.endswith("_1d_spot.csv"):
            symbol = file.replace("_1d_spot.csv", "").replace("USDT", "")
            available_coins.append(symbol)
    
    return list(set(available_coins))

def load_price_data(symbol):
    """加载价格数据"""
    # 尝试不同的文件路径
    possible_paths = [
        f"{symbol}USDT_1d_spot.csv",
        f"BTC定投/{symbol}USDT_1d_spot.csv",
        f"{symbol}_1d_spot.csv",
        f"BTC定投/{symbol}_1d_spot.csv"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            try:
                data = pd.read_csv(path)
                p = data[['open_time', 'open']].copy()
                p["date"] = pd.to_datetime(p["open_time"], unit="ms").dt.date
                p = p.rename(columns={"open": "price"})
                p = p[["date","price"]].drop_duplicates("date").set_index("date").sort_index()
                
                # 限制时间范围
                start_date = pd.to_datetime(START).date()
                end_date = pd.to_datetime(END).date()
                p = p.loc[(p.index >= start_date) & (p.index <= end_date)]
                
                if not p.empty:
                    print(f"✓ 成功加载 {symbol} 数据: {len(p)} 条记录")
                    return p
            except Exception as e:
                print(f"读取 {path} 失败: {e}")
                continue
    
    print(f"✗ 未找到 {symbol} 的数据文件")
    return pd.DataFrame()

def make_daily_data(p):
    """处理为每日数据"""
    if p.empty:
        return pd.DataFrame()
    
    # 创建完整的日期范围并前向填充
    idx = pd.date_range(p.index.min(), p.index.max(), freq="D").date
    d = p.reindex(idx).ffill()
    d.index.name = "date"
    d = d.rename(columns={"price": "close"})
    return d

def backtest_simple_dca(symbol, price_data):
    """简单定投回测"""
    if price_data.empty:
        return None
    
    d = make_daily_data(price_data)
    if d.empty:
        return None
    
    # 生成定投日期
    invest_dates = pd.date_range(d.index.min(), d.index.max(), freq=FREQ).date
    invest_dates = [date for date in invest_dates if date in d.index]
    
    if not invest_dates:
        return None
    
    # 定投模拟
    total_qty = 0.0
    total_cost = 0.0
    equity_curve = []
    
    for date in invest_dates:
        price = d.loc[date, "close"]
        fee_factor = 1 - FEE_BPS / 100.0
        
        # 购买
        qty_bought = BASE_CONTRIB / price * fee_factor
        total_qty += qty_bought
        total_cost += BASE_CONTRIB
        
        # 记录权益
        current_value = total_qty * price
        equity_curve.append({
            'date': date,
            'price': price,
            'qty': total_qty,
            'cost': total_cost,
            'value': current_value,
            'return_pct': (current_value / total_cost - 1) * 100 if total_cost > 0 else 0
        })
    
    if not equity_curve:
        return None
    
    # 计算最终统计
    final_record = equity_curve[-1]
    final_value = final_record['value']
    total_return = final_record['return_pct']
    
    # 计算年化收益率
    days = (invest_dates[-1] - invest_dates[0]).days
    years = days / 365.25 if days > 0 else 1
    cagr = (final_value / total_cost) ** (1 / years) - 1 if total_cost > 0 else 0
    
    # 计算波动率（基于权益曲线）
    equity_values = [record['value'] for record in equity_curve]
    if len(equity_values) > 1:
        returns = np.diff(equity_values) / equity_values[:-1]
        volatility = np.std(returns) * np.sqrt(52)  # 周频率年化
        sharpe = (cagr - 0) / (volatility + 1e-7)
    else:
        volatility = 0
        sharpe = 0
    
    # 计算最大回撤
    peak_value = 0
    max_drawdown = 0
    for record in equity_curve:
        value = record['value']
        if value > peak_value:
            peak_value = value
        drawdown = (peak_value - value) / peak_value if peak_value > 0 else 0
        max_drawdown = max(max_drawdown, drawdown)
    
    return {
        'symbol': symbol,
        'final_value': final_value,
        'total_cost': total_cost,
        'total_return': total_return,
        'cagr': cagr * 100,
        'volatility': volatility * 100,
        'sharpe': sharpe,
        'max_drawdown': max_drawdown * 100,
        'invest_count': len(invest_dates),
        'equity_curve': equity_curve
    }

def run_quick_backtest():
    """运行快速回测"""
    print("=== 快速多币种定投回测 ===")
    print(f"回测期间: {START} 至 {END}")
    print(f"定投频率: 每周一")
    print(f"每期投入: ${BASE_CONTRIB}")
    print(f"手续费: {FEE_BPS}%\n")
    
    # 检查可用币种
    available_coins = check_available_coins()
    print(f"发现可用币种: {available_coins}\n")
    
    if not available_coins:
        print("未找到任何币种数据文件！")
        print("请确保以下文件存在:")
        print("- BTCUSDT_1d_spot.csv")
        print("- ETHUSDT_1d_spot.csv")
        print("- 或其他币种的日K线数据文件")
        return []
    
    results = []
    
    for coin in available_coins:
        print(f"正在回测 {coin}...")
        price_data = load_price_data(coin)
        
        if not price_data.empty:
            result = backtest_simple_dca(coin, price_data)
            if result:
                results.append(result)
                print(f"  ✓ {coin} 回测完成")
            else:
                print(f"  ✗ {coin} 回测失败")
        else:
            print(f"  ✗ {coin} 数据加载失败")
    
    if not results:
        print("\n没有成功回测的币种！")
        return []
    
    # 显示结果
    print(f"\n=== 回测结果汇总 ({len(results)} 个币种) ===")
    print(f"{'币种':<8} {'最终价值':<12} {'投入成本':<12} {'总收益':<10} {'年化收益':<10} {'夏普比率':<8} {'最大回撤':<8} {'投资次数':<8}")
    print("-" * 85)
    
    for r in results:
        print(f"{r['symbol']:<8} ${r['final_value']:<11,.0f} ${r['total_cost']:<11,.0f} "
              f"{r['total_return']:<9.1f}% {r['cagr']:<9.1f}% {r['sharpe']:<7.2f} "
              f"{r['max_drawdown']:<7.1f}% {r['invest_count']:<8}")
    
    # 保存详细结果
    df = pd.DataFrame([
        {
            "币种": r["symbol"],
            "最终价值(USD)": r["final_value"],
            "投入成本(USD)": r["total_cost"],
            "总收益率(%)": r["total_return"],
            "年化收益率(%)": r["cagr"],
            "年化波动率(%)": r["volatility"],
            "夏普比率": r["sharpe"],
            "最大回撤(%)": r["max_drawdown"],
            "投资次数": r["invest_count"]
        }
        for r in results
    ])
    
    filename = f"定投回测结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"\n详细结果已保存至: {filename}")
    
    return results

def plot_comparison(results):
    """绘制对比图"""
    if not results:
        return
    
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Heiti SC', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 总收益率对比
    symbols = [r['symbol'] for r in results]
    returns = [r['total_return'] for r in results]
    
    bars1 = ax1.bar(symbols, returns, color='skyblue', alpha=0.7)
    ax1.set_title('总收益率对比', fontsize=12)
    ax1.set_ylabel('总收益率 (%)')
    ax1.grid(True, alpha=0.3)
    
    for bar, value in zip(bars1, returns):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + max(returns)*0.01,
                f'{value:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 2. 年化收益率对比
    cagrs = [r['cagr'] for r in results]
    bars2 = ax2.bar(symbols, cagrs, color='lightgreen', alpha=0.7)
    ax2.set_title('年化收益率对比', fontsize=12)
    ax2.set_ylabel('年化收益率 (%)')
    ax2.grid(True, alpha=0.3)
    
    for bar, value in zip(bars2, cagrs):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + max(cagrs)*0.01,
                f'{value:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 3. 夏普比率对比
    sharpes = [r['sharpe'] for r in results]
    bars3 = ax3.bar(symbols, sharpes, color='orange', alpha=0.7)
    ax3.set_title('夏普比率对比', fontsize=12)
    ax3.set_ylabel('夏普比率')
    ax3.grid(True, alpha=0.3)
    
    for bar, value in zip(bars3, sharpes):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(sharpes)*0.01,
                f'{value:.2f}', ha='center', va='bottom', fontsize=9)
    
    # 4. 最大回撤对比
    drawdowns = [r['max_drawdown'] for r in results]
    bars4 = ax4.bar(symbols, drawdowns, color='salmon', alpha=0.7)
    ax4.set_title('最大回撤对比', fontsize=12)
    ax4.set_ylabel('最大回撤 (%)')
    ax4.grid(True, alpha=0.3)
    
    for bar, value in zip(bars4, drawdowns):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + max(drawdowns)*0.01,
                f'{value:.1f}%', ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    
    filename = f"定投对比图_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    print(f"对比图已保存至: {filename}")
    plt.show()

if __name__ == "__main__":
    results = run_quick_backtest()
    
    if results:
        # 绘制对比图
        try:
            plot_comparison(results)
        except Exception as e:
            print(f"绘图失败: {e}")
        
        # 简单分析
        if len(results) > 1:
            best = max(results, key=lambda x: x['total_return'])
            worst = min(results, key=lambda x: x['total_return'])
            best_sharpe = max(results, key=lambda x: x['sharpe'])
            
            print(f"\n=== 简单分析 ===")
            print(f"收益最高: {best['symbol']} ({best['total_return']:.1f}%)")
            print(f"收益最低: {worst['symbol']} ({worst['total_return']:.1f}%)")
            print(f"夏普最佳: {best_sharpe['symbol']} ({best_sharpe['sharpe']:.2f})")
            
            # 等权重组合
            total_value = sum(r['final_value'] for r in results)
            total_cost = sum(r['total_cost'] for r in results)
            portfolio_return = (total_value / total_cost - 1) * 100
            print(f"等权重组合收益: {portfolio_return:.1f}%")
