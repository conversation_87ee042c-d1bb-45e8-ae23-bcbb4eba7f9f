# download_and_backtest.py
# 下载多币种数据并进行定投回测
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import time
from binance.client import Client
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# ---------------- 参数配置 ----------------
START = "2020-01-01"
END   = "2025-08-13"
FREQ  = "W-MON"                           # 每周一定投
BASE_CONTRIB = 100.0                      # 每期投入100美元
FEE_BPS = 0.1                             # 手续费0.1%

# 要回测的币种列表
TARGET_COINS = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'DOGE', 'XRP', 'TRX', 'MATIC', 'DOT']

client = Client()

def download_coin_data(symbol):
    """下载单个币种的历史数据"""
    symbol_usdt = f"{symbol}USDT"
    file_name = f"{symbol_usdt}_1d_spot.csv"
    
    # 如果文件已存在，跳过下载
    if os.path.exists(file_name):
        print(f"✓ {symbol} 数据文件已存在，跳过下载")
        return True
    
    print(f"正在下载 {symbol} 数据...")
    
    try:
        startTime = 1577808000000  # 2020-01-01
        total = 0
        
        # 创建CSV文件头
        with open(file_name, "w") as fp:
            print("open_time,open,high,low,close,volume,close_time,quote_volume,count,taker_buy_volume,taker_buy_quote_volume,ignore", file=fp)
        
        while True:
            try:
                result = client.get_historical_klines(symbol_usdt, Client.KLINE_INTERVAL_1DAY, start_str=str(startTime), limit=1000)
                
                if not result:
                    break
                
                # 写入数据
                with open(file_name, "a") as fp:
                    for item in result:
                        print(",".join(str(x) for x in item), file=fp)
                
                total += len(result)
                print(f"  已下载 {total} 条 {symbol} 数据")
                
                # 如果返回的数据少于1000条，说明已经到了最新数据
                if len(result) < 1000:
                    break
                
                startTime = result[-1][0] + 1
                time.sleep(0.1)  # 避免请求过快
                
            except Exception as e:
                print(f"  下载 {symbol} 数据时出错: {e}")
                if hasattr(e, 'error_code') and e.error_code == -1121:
                    print(f"  {symbol} 交易对不存在")
                    return False
                time.sleep(5)
                continue
        
        print(f"✓ {symbol} 数据下载完成，共 {total} 条记录")
        return True
        
    except Exception as e:
        print(f"✗ {symbol} 数据下载失败: {e}")
        return False

def load_and_process_data(symbol):
    """加载并处理币种数据"""
    symbol_usdt = f"{symbol}USDT"
    file_path = f'{symbol_usdt}_1d_spot.csv'
    
    if not os.path.exists(file_path):
        return pd.DataFrame()
    
    try:
        data = pd.read_csv(file_path)
        if data.empty:
            return pd.DataFrame()
        
        p = data[['open_time', 'open']].copy()
        p["date"] = pd.to_datetime(p["open_time"], unit="ms").dt.date
        p = p.rename(columns={"open": "price"})
        p = p[["date","price"]].drop_duplicates("date").set_index("date").sort_index()
        
        # 限制时间范围
        start_date = pd.to_datetime(START).date()
        end_date = pd.to_datetime(END).date()
        p = p.loc[(p.index >= start_date) & (p.index <= end_date)]
        
        # 创建完整的日期范围并前向填充
        if not p.empty:
            idx = pd.date_range(p.index.min(), p.index.max(), freq="D").date
            d = p.reindex(idx).ffill()
            d.index.name = "date"
            d = d.rename(columns={"price": "close"})
            return d
        
        return pd.DataFrame()
        
    except Exception as e:
        print(f"处理 {symbol} 数据失败: {e}")
        return pd.DataFrame()

def backtest_dca(symbol, price_data):
    """定投回测"""
    if price_data.empty:
        return None
    
    # 生成定投日期
    invest_dates = pd.date_range(price_data.index.min(), price_data.index.max(), freq=FREQ).date
    invest_dates = [date for date in invest_dates if date in price_data.index]
    
    if not invest_dates:
        return None
    
    # 定投模拟
    total_qty = 0.0
    total_cost = 0.0
    equity_records = []
    
    for date in invest_dates:
        price = price_data.loc[date, "close"]
        fee_factor = 1 - FEE_BPS / 100.0
        
        # 购买
        qty_bought = BASE_CONTRIB / price * fee_factor
        total_qty += qty_bought
        total_cost += BASE_CONTRIB
        
        # 记录权益
        current_value = total_qty * price
        equity_records.append({
            'date': date,
            'price': price,
            'qty': total_qty,
            'cost': total_cost,
            'value': current_value
        })
    
    if not equity_records:
        return None
    
    # 计算统计指标
    final_record = equity_records[-1]
    final_value = final_record['value']
    total_return = (final_value / total_cost - 1) * 100 if total_cost > 0 else 0
    
    # 计算年化收益率
    days = (invest_dates[-1] - invest_dates[0]).days
    years = days / 365.25 if days > 0 else 1
    cagr = (final_value / total_cost) ** (1 / years) - 1 if total_cost > 0 else 0
    
    # 计算波动率
    equity_values = [record['value'] for record in equity_records]
    if len(equity_values) > 1:
        returns = np.diff(equity_values) / equity_values[:-1]
        volatility = np.std(returns) * np.sqrt(52)  # 周频率年化
        sharpe = (cagr - 0) / (volatility + 1e-7)
    else:
        volatility = 0
        sharpe = 0
    
    # 计算最大回撤
    peak_value = 0
    max_drawdown = 0
    for record in equity_records:
        value = record['value']
        if value > peak_value:
            peak_value = value
        drawdown = (peak_value - value) / peak_value if peak_value > 0 else 0
        max_drawdown = max(max_drawdown, drawdown)
    
    return {
        'symbol': symbol,
        'final_value': final_value,
        'total_cost': total_cost,
        'total_return': total_return,
        'cagr': cagr * 100,
        'volatility': volatility * 100,
        'sharpe': sharpe,
        'max_drawdown': max_drawdown * 100,
        'invest_count': len(invest_dates),
        'first_date': invest_dates[0],
        'last_date': invest_dates[-1],
        'equity_records': equity_records
    }

def run_comprehensive_backtest():
    """运行综合回测"""
    print("=== 多币种定投回测系统 ===")
    print(f"回测期间: {START} 至 {END}")
    print(f"定投频率: 每周一")
    print(f"每期投入: ${BASE_CONTRIB}")
    print(f"手续费: {FEE_BPS}%")
    print(f"目标币种: {', '.join(TARGET_COINS)}\n")
    
    # 第一步：下载数据
    print("=== 第一步：下载历史数据 ===")
    successful_downloads = []
    
    for coin in TARGET_COINS:
        if download_coin_data(coin):
            successful_downloads.append(coin)
        time.sleep(1)  # 避免请求过快
    
    print(f"\n成功下载 {len(successful_downloads)} 个币种的数据")
    
    # 第二步：回测
    print("\n=== 第二步：执行定投回测 ===")
    results = []
    
    for coin in successful_downloads:
        print(f"正在回测 {coin}...")
        price_data = load_and_process_data(coin)
        
        if not price_data.empty:
            result = backtest_dca(coin, price_data)
            if result:
                results.append(result)
                print(f"  ✓ {coin} 回测完成 - 总收益: {result['total_return']:.1f}%")
            else:
                print(f"  ✗ {coin} 回测失败")
        else:
            print(f"  ✗ {coin} 数据加载失败")
    
    if not results:
        print("没有成功回测的币种！")
        return []
    
    # 第三步：结果分析
    print(f"\n=== 第三步：结果分析 ({len(results)} 个币种) ===")
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return'], reverse=True)
    
    # 显示结果表格
    print(f"{'排名':<4} {'币种':<6} {'最终价值':<12} {'投入成本':<12} {'总收益':<10} {'年化收益':<10} {'夏普比率':<8} {'最大回撤':<8}")
    print("-" * 80)
    
    for i, r in enumerate(results, 1):
        print(f"{i:<4} {r['symbol']:<6} ${r['final_value']:<11,.0f} ${r['total_cost']:<11,.0f} "
              f"{r['total_return']:<9.1f}% {r['cagr']:<9.1f}% {r['sharpe']:<7.2f} {r['max_drawdown']:<7.1f}%")
    
    # 保存详细结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # CSV结果
    df = pd.DataFrame([
        {
            "排名": i,
            "币种": r["symbol"],
            "最终价值(USD)": r["final_value"],
            "投入成本(USD)": r["total_cost"],
            "总收益率(%)": r["total_return"],
            "年化收益率(%)": r["cagr"],
            "年化波动率(%)": r["volatility"],
            "夏普比率": r["sharpe"],
            "最大回撤(%)": r["max_drawdown"],
            "投资次数": r["invest_count"],
            "开始日期": r["first_date"],
            "结束日期": r["last_date"]
        }
        for i, r in enumerate(results, 1)
    ])
    
    csv_filename = f"多币种定投回测_{timestamp}.csv"
    df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
    print(f"\n详细结果已保存至: {csv_filename}")
    
    # 简单统计
    print(f"\n=== 投资组合统计 ===")
    total_value = sum(r['final_value'] for r in results)
    total_cost = sum(r['total_cost'] for r in results)
    portfolio_return = (total_value / total_cost - 1) * 100
    
    print(f"等权重投资组合:")
    print(f"  总投入: ${total_cost:,.0f}")
    print(f"  总价值: ${total_value:,.0f}")
    print(f"  组合收益: {portfolio_return:.1f}%")
    
    best = results[0]
    worst = results[-1]
    best_sharpe = max(results, key=lambda x: x['sharpe'])
    
    print(f"\n表现最佳: {best['symbol']} ({best['total_return']:.1f}%)")
    print(f"表现最差: {worst['symbol']} ({worst['total_return']:.1f}%)")
    print(f"夏普最佳: {best_sharpe['symbol']} ({best_sharpe['sharpe']:.2f})")
    
    return results

if __name__ == "__main__":
    results = run_comprehensive_backtest()
    
    if results:
        print(f"\n回测完成！成功分析了 {len(results)} 个币种的定投表现。")
        print("您可以查看生成的CSV文件获取详细数据。")
