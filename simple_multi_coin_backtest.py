# simple_multi_coin_backtest.py
# 简化版多币种定投回测（使用yfinance获取数据）
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# ---------------- 参数配置 ----------------
START = "2020-01-01"
END   = "2025-08-13"
FREQ  = "W-MON"                           # 每周一定投
BASE_CONTRIB = 100.0                      # 每期投入100美元
FEE_BPS = 0.1                             # 手续费0.1%

# 要回测的币种列表（使用Yahoo Finance的代码）
TARGET_COINS = {
    'BTC': 'BTC-USD',
    'ETH': 'ETH-USD', 
    'BNB': 'BNB-USD',
    'ADA': 'ADA-USD',
    'SOL': 'SOL-USD',
    'DOGE': 'DOGE-USD',
    'XRP': 'XRP-USD',
    'TRX': 'TRX-USD',
    'MATIC': 'MATIC-USD',
    'DOT': 'DOT-USD'
}

def download_crypto_data(symbol, yahoo_symbol):
    """使用yfinance下载加密货币数据"""
    try:
        print(f"正在下载 {symbol} 数据...")
        ticker = yf.Ticker(yahoo_symbol)
        data = ticker.history(start=START, end=END, interval="1d")
        
        if data.empty:
            print(f"  ✗ {symbol} 数据为空")
            return pd.DataFrame()
        
        # 处理数据格式
        price_data = data[['Open']].copy()
        price_data = price_data.rename(columns={'Open': 'close'})
        price_data.index = price_data.index.date
        price_data.index.name = 'date'
        
        # 填充缺失日期
        idx = pd.date_range(price_data.index.min(), price_data.index.max(), freq="D").date
        price_data = price_data.reindex(idx).ffill()
        
        print(f"  ✓ {symbol} 数据下载成功，共 {len(price_data)} 条记录")
        return price_data
        
    except Exception as e:
        print(f"  ✗ {symbol} 数据下载失败: {e}")
        return pd.DataFrame()

def backtest_dca(symbol, price_data):
    """定投回测"""
    if price_data.empty:
        return None
    
    # 生成定投日期
    invest_dates = pd.date_range(price_data.index.min(), price_data.index.max(), freq=FREQ).date
    invest_dates = [date for date in invest_dates if date in price_data.index]
    
    if not invest_dates:
        return None
    
    # 定投模拟
    total_qty = 0.0
    total_cost = 0.0
    equity_records = []
    
    for date in invest_dates:
        price = price_data.loc[date, "close"]
        fee_factor = 1 - FEE_BPS / 100.0
        
        # 购买
        qty_bought = BASE_CONTRIB / price * fee_factor
        total_qty += qty_bought
        total_cost += BASE_CONTRIB
        
        # 记录权益
        current_value = total_qty * price
        equity_records.append({
            'date': date,
            'price': price,
            'qty': total_qty,
            'cost': total_cost,
            'value': current_value
        })
    
    if not equity_records:
        return None
    
    # 计算统计指标
    final_record = equity_records[-1]
    final_value = final_record['value']
    total_return = (final_value / total_cost - 1) * 100 if total_cost > 0 else 0
    
    # 计算年化收益率
    days = (invest_dates[-1] - invest_dates[0]).days
    years = days / 365.25 if days > 0 else 1
    cagr = (final_value / total_cost) ** (1 / years) - 1 if total_cost > 0 else 0
    
    # 计算波动率
    equity_values = [record['value'] for record in equity_records]
    if len(equity_values) > 1:
        returns = np.diff(equity_values) / equity_values[:-1]
        volatility = np.std(returns) * np.sqrt(52)  # 周频率年化
        sharpe = (cagr - 0) / (volatility + 1e-7)
    else:
        volatility = 0
        sharpe = 0
    
    # 计算最大回撤
    peak_value = 0
    max_drawdown = 0
    for record in equity_records:
        value = record['value']
        if value > peak_value:
            peak_value = value
        drawdown = (peak_value - value) / peak_value if peak_value > 0 else 0
        max_drawdown = max(max_drawdown, drawdown)
    
    return {
        'symbol': symbol,
        'final_value': final_value,
        'total_cost': total_cost,
        'total_return': total_return,
        'cagr': cagr * 100,
        'volatility': volatility * 100,
        'sharpe': sharpe,
        'max_drawdown': max_drawdown * 100,
        'invest_count': len(invest_dates),
        'first_date': invest_dates[0],
        'last_date': invest_dates[-1],
        'equity_records': equity_records
    }

def plot_results(results):
    """绘制结果对比图"""
    if not results:
        return
    
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 总收益率对比
    symbols = [r['symbol'] for r in results]
    returns = [r['total_return'] for r in results]
    
    bars1 = ax1.bar(symbols, returns, color='skyblue', alpha=0.7)
    ax1.set_title('Total Return Comparison', fontsize=12)
    ax1.set_ylabel('Total Return (%)')
    ax1.grid(True, alpha=0.3)
    
    for bar, value in zip(bars1, returns):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + max(returns)*0.01,
                f'{value:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 2. 年化收益率对比
    cagrs = [r['cagr'] for r in results]
    bars2 = ax2.bar(symbols, cagrs, color='lightgreen', alpha=0.7)
    ax2.set_title('Annualized Return Comparison', fontsize=12)
    ax2.set_ylabel('CAGR (%)')
    ax2.grid(True, alpha=0.3)
    
    for bar, value in zip(bars2, cagrs):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + max(cagrs)*0.01,
                f'{value:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 3. 夏普比率对比
    sharpes = [r['sharpe'] for r in results]
    bars3 = ax3.bar(symbols, sharpes, color='orange', alpha=0.7)
    ax3.set_title('Sharpe Ratio Comparison', fontsize=12)
    ax3.set_ylabel('Sharpe Ratio')
    ax3.grid(True, alpha=0.3)
    
    for bar, value in zip(bars3, sharpes):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(sharpes)*0.01,
                f'{value:.2f}', ha='center', va='bottom', fontsize=9)
    
    # 4. 最大回撤对比
    drawdowns = [r['max_drawdown'] for r in results]
    bars4 = ax4.bar(symbols, drawdowns, color='salmon', alpha=0.7)
    ax4.set_title('Maximum Drawdown Comparison', fontsize=12)
    ax4.set_ylabel('Max Drawdown (%)')
    ax4.grid(True, alpha=0.3)
    
    for bar, value in zip(bars4, drawdowns):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + max(drawdowns)*0.01,
                f'{value:.1f}%', ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    
    filename = f"crypto_dca_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    print(f"对比图已保存至: {filename}")
    plt.show()

def run_crypto_backtest():
    """运行加密货币定投回测"""
    print("=== 加密货币定投回测系统 ===")
    print(f"回测期间: {START} 至 {END}")
    print(f"定投频率: 每周一")
    print(f"每期投入: ${BASE_CONTRIB}")
    print(f"手续费: {FEE_BPS}%")
    print(f"目标币种: {', '.join(TARGET_COINS.keys())}\n")
    
    results = []
    
    for symbol, yahoo_symbol in TARGET_COINS.items():
        price_data = download_crypto_data(symbol, yahoo_symbol)
        
        if not price_data.empty:
            result = backtest_dca(symbol, price_data)
            if result:
                results.append(result)
                print(f"  ✓ {symbol} 回测完成 - 总收益: {result['total_return']:.1f}%")
            else:
                print(f"  ✗ {symbol} 回测失败")
        else:
            print(f"  ✗ {symbol} 数据获取失败")
    
    if not results:
        print("没有成功回测的币种！")
        return []
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return'], reverse=True)
    
    # 显示结果
    print(f"\n=== 回测结果 ({len(results)} 个币种) ===")
    print(f"{'排名':<4} {'币种':<6} {'最终价值':<12} {'投入成本':<12} {'总收益':<10} {'年化收益':<10} {'夏普比率':<8} {'最大回撤':<8}")
    print("-" * 80)
    
    for i, r in enumerate(results, 1):
        print(f"{i:<4} {r['symbol']:<6} ${r['final_value']:<11,.0f} ${r['total_cost']:<11,.0f} "
              f"{r['total_return']:<9.1f}% {r['cagr']:<9.1f}% {r['sharpe']:<7.2f} {r['max_drawdown']:<7.1f}%")
    
    # 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    df = pd.DataFrame([
        {
            "排名": i,
            "币种": r["symbol"],
            "最终价值(USD)": r["final_value"],
            "投入成本(USD)": r["total_cost"],
            "总收益率(%)": r["total_return"],
            "年化收益率(%)": r["cagr"],
            "年化波动率(%)": r["volatility"],
            "夏普比率": r["sharpe"],
            "最大回撤(%)": r["max_drawdown"],
            "投资次数": r["invest_count"]
        }
        for i, r in enumerate(results, 1)
    ])
    
    csv_filename = f"crypto_dca_results_{timestamp}.csv"
    df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
    print(f"\n详细结果已保存至: {csv_filename}")
    
    # 投资组合统计
    print(f"\n=== 投资组合统计 ===")
    total_value = sum(r['final_value'] for r in results)
    total_cost = sum(r['total_cost'] for r in results)
    portfolio_return = (total_value / total_cost - 1) * 100
    
    print(f"等权重投资组合:")
    print(f"  总投入: ${total_cost:,.0f}")
    print(f"  总价值: ${total_value:,.0f}")
    print(f"  组合收益: {portfolio_return:.1f}%")
    
    if len(results) > 0:
        best = results[0]
        worst = results[-1]
        best_sharpe = max(results, key=lambda x: x['sharpe'])
        
        print(f"\n表现最佳: {best['symbol']} ({best['total_return']:.1f}%)")
        print(f"表现最差: {worst['symbol']} ({worst['total_return']:.1f}%)")
        print(f"夏普最佳: {best_sharpe['symbol']} ({best_sharpe['sharpe']:.2f})")
    
    return results

if __name__ == "__main__":
    # 首先尝试安装yfinance
    try:
        import yfinance as yf
    except ImportError:
        print("正在安装 yfinance...")
        import subprocess
        subprocess.check_call(['pip', 'install', 'yfinance'])
        import yfinance as yf
    
    results = run_crypto_backtest()
    
    if results:
        print(f"\n回测完成！成功分析了 {len(results)} 个币种的定投表现。")
        
        # 绘制对比图
        try:
            plot_results(results)
        except Exception as e:
            print(f"绘图失败: {e}")
        
        print("您可以查看生成的CSV文件获取详细数据。")
